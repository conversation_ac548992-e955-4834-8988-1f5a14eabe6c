# MCDN

这是一个使用 Go 语言开发的多云CDN调度管理平台，基于 etcd 实现资源监听和控制器模式，提供完整的 CDN 分发、DNS 解析和路由调度管理能力。

## 项目架构

MCDN 采用控制器模式（Controller Pattern），通过监听 etcd 中的资源变化来执行相应的调度任务。

### 核心组件

- **API Server**: 提供 RESTful API 接口
- **Controller Manager**: 管理各种资源控制器
- **Resource Controllers**: 处理具体的资源类型
  - Distribution Controller: CDN 分发管理
  - DNS Controller: DNS 解析管理
  - Policy Controller: 路由调度管理
- **Worker**: 任务执行引擎

## 项目结构

```bash
mcdn/
├── README.md                    # 项目说明
├── go.mod                       # Go 模块定义
├── go.sum                       # 依赖校验文件
├── config.example.yaml          # 配置文件示例
├── config.schema.json           # 配置文件 JSON Schema
├── docker-compose.dev.yml       # 开发环境 Docker Compose
├── Taskfile.yml                 # Task 构建配置
├── TASKFILE.md                  # Task 使用说明
├── ETCD_DEV.md                  # etcd 开发环境说明
├── .golangci.yml               # Go 代码检查配置
├── .gitignore                  # Git 忽略文件
├── api/                        # API 定义
│   └── v1/                     # v1 版本 API
│       ├── distribution.go     # CDN 分发资源定义
│       ├── dns.go             # DNS 资源定义
│       ├── policy.go           # 路由资源定义
│       └── types.go           # 通用类型定义
├── cmd/                        # 命令行入口
│   └── mcdn/
│       └── main.go            # 主程序入口
├── pkg/                        # 公共包
│   ├── app/                   # 应用框架
│   │   ├── app.go            # CLI 应用定义
│   │   └── commands/         # 命令实现
│   │       ├── commands.go   # 命令注册
│   │       ├── apiserver.go  # API 服务器命令
│   │       ├── config.go     # 配置命令
│   │       ├── controller.go # 控制器命令
│   │       └── run.go        # 运行命令
│   ├── apiserver/            # API 服务器
│   │   └── server.go         # HTTP 服务器实现
│   ├── config/               # 配置管理
│   │   ├── config.go         # 配置结构定义
│   │   ├── defaults.go       # 默认配置
│   │   └── schema.go         # 配置验证
│   ├── version/              # 版本信息
│   │   └── version.go        # 版本定义
│   └── worker/               # 任务执行器
│       └── worker.go         # Worker 实现
├── internal/                   # 内部包
│   ├── controller/            # 控制器框架
│   │   ├── controller.go     # 基础控制器
│   │   ├── registry.go       # 控制器注册管理
│   │   └── watch.go          # etcd 监听器
│   ├── logger/               # 日志组件
│   │   └── logger.go         # 日志实现
│   └── resource/             # 资源控制器
│       ├── distribution/     # CDN 分发控制器
│       │   └── controller.go
│       ├── dns/              # DNS 控制器
│       │   ├── controller.go
│       │   └── types.go
│       └── route/            # 路由控制器
│           └── controller.go
└── .vscode/                   # VS Code 配置
```

## 功能特性

### 资源管理

- **CDN Distribution**: 管理多云厂商的 CDN 分发配置
- **DNS Zone/Record**: 管理 DNS 解析配置和记录
- **Policy**: 管理智能路由调度策略

### 调度策略

- **负载均衡**: 基于权重的流量分发
- **故障转移**: 自动故障检测和切换
- **地理路由**: 基于地理位置的智能调度
- **健康检查**: 实时监控服务健康状态
- **限流控制**: 请求频率限制和保护
- **灰度发布**: 金丝雀部署支持
- **定时调度**: 基于 Cron 表达式的定时任务

### 条件匹配

支持多种条件类型的智能匹配：

- 地理位置 (GeoLocation)
- 用户代理 (UserAgent)
- 请求路径 (RequestPath)
- 请求头 (RequestHeader)
- 源 IP (SourceIP)
- 时间条件 (Time)
- 健康状态 (HealthStatus)
- 请求频率 (RequestRate)

## 开发环境

### 前置要求

- Go 1.24.6+
- Docker & Docker Compose
- Task (可选，用于构建)

### 启动开发环境

1. **启动 etcd 服务**

   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

2. **复制配置文件**

   ```bash
   cp config.example.yaml config.yaml
   ```

3. **安装依赖**

   ```bash
   go mod download
   ```

### 构建和运行

使用 Task (推荐):

```bash
# 查看所有可用任务
task --list

# 开发模式运行
task dev

# 构建并运行
task run

# 构建优化版本
task build

# 代码格式化
task format

# 代码检查
task lint

# 运行测试
task test
```

使用 Go 命令:

```bash
# 构建
go build -o mcdn cmd/mcdn/main.go

# 运行
./mcdn --help
```

## 使用方式

### 命令行选项

```bash
# 查看帮助
./mcdn --help

# 指定配置文件
./mcdn --config /path/to/config.yaml

# 运行控制器
./mcdn controller

# 运行 API 服务器
./mcdn apiserver

# 运行完整服务
./mcdn run
```

### 配置文件

参考 [`config.example.yaml`](config.example.yaml) 创建配置文件：

```yaml
env: dev
log:
  level: info
  path: ./log/app.log
worker:
  concurrency: 4
  queue: default
etcd:
  endpoints:
    - localhost:2379
  timeout: 5s
```

配置文件支持 JSON Schema 验证，详见 [`config.schema.json`](config.schema.json)。

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t mcdn .

# 运行容器
docker run -d \
  --name mcdn \
  -v /path/to/config.yaml:/app/config.yaml \
  mcdn
```

### 生产环境

1. 使用 `task build` 构建优化版本
2. 配置生产环境的 etcd 集群
3. 调整配置文件中的日志级别和并发数
4. 设置适当的监控和告警

## 监控和日志

- 日志输出支持结构化格式
- 支持多种日志级别 (trace, debug, info, warn, error, fatal, panic)
- 内置性能指标收集
- 支持分布式链路追踪

## 开发指南

### 添加新的资源控制器

1. 在 `api/v1/` 中定义资源类型
2. 在 `internal/resource/` 中实现控制器
3. 在控制器管理器中注册新控制器
4. 添加相应的测试用例

### 代码规范

- 遵循 Go 官方代码规范
- 使用 `goimports` 和 `gofumpt` 格式化代码
- 通过 `golangci-lint` 代码检查
- 编写单元测试和集成测试

## 许可证

本项目为内部项目，版权归 Lilith Games 所有。
